import torch
import torch.nn as nn
import torch.optim as optim
import torchvision
import torchvision.transforms as transforms
import time
from wide_resnet_original import wide_resnet_28_10

def setup_quick_data_loaders(batch_size=128, subset_size=1000):
    """Setup CIFAR-10 data loaders with subset for quick testing"""
    print(f"Setting up CIFAR-10 data loaders (subset: {subset_size} samples)...")
    
    # Data preprocessing
    transform_train = transforms.Compose([
        transforms.RandomCrop(32, padding=4),
        transforms.RandomHorizontalFlip(),
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
    ])

    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
    ])

    # Load full datasets
    full_trainset = torchvision.datasets.CIFAR10(root='./data', train=True, 
                                                 download=False, transform=transform_train)
    full_testset = torchvision.datasets.CIFAR10(root='./data', train=False, 
                                                download=False, transform=transform_test)
    
    # Create subsets for quick training
    train_indices = torch.randperm(len(full_trainset))[:subset_size]
    test_indices = torch.randperm(len(full_testset))[:subset_size//5]  # Smaller test set
    
    trainset = torch.utils.data.Subset(full_trainset, train_indices)
    testset = torch.utils.data.Subset(full_testset, test_indices)
    
    # Create data loaders
    trainloader = torch.utils.data.DataLoader(trainset, batch_size=batch_size, 
                                             shuffle=True, num_workers=2)
    testloader = torch.utils.data.DataLoader(testset, batch_size=100, 
                                            shuffle=False, num_workers=2)
    
    print(f"Training subset size: {len(trainset)}")
    print(f"Test subset size: {len(testset)}")
    print(f"Number of training batches: {len(trainloader)}")
    print(f"Number of test batches: {len(testloader)}")
    
    return trainloader, testloader

def quick_train(epochs=3, subset_size=1000):
    """Quick training function for testing"""
    print("="*50)
    print("QUICK TRAINING SESSION")
    print("="*50)
    
    # Setup
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Setup data
    trainloader, testloader = setup_quick_data_loaders(batch_size=64, subset_size=subset_size)
    
    # Setup model
    model = wide_resnet_28_10(num_classes=10, input_channels=3).to(device)
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Setup training
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.SGD(model.parameters(), lr=0.1, momentum=0.9, weight_decay=5e-4)
    
    print(f"Training for {epochs} epochs on {subset_size} samples")
    print("-"*50)
    
    # Training loop
    start_time = time.time()
    
    for epoch in range(epochs):
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0
        
        print(f"Epoch {epoch+1}/{epochs}")
        
        for batch_idx, (data, target) in enumerate(trainloader):
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
            
            running_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
            
            if batch_idx % 5 == 0:  # Print more frequently for quick feedback
                print(f"  Batch {batch_idx}/{len(trainloader)}, Loss: {loss.item():.4f}")
        
        # Epoch results
        epoch_loss = running_loss / len(trainloader)
        epoch_acc = 100. * correct / total
        
        print(f"  Train Loss: {epoch_loss:.4f}")
        print(f"  Train Accuracy: {epoch_acc:.2f}%")
        
        # Quick test
        model.eval()
        test_correct = 0
        test_total = 0
        
        with torch.no_grad():
            for data, target in testloader:
                data, target = data.to(device), target.to(device)
                outputs = model(data)
                _, predicted = torch.max(outputs, 1)
                test_total += target.size(0)
                test_correct += (predicted == target).sum().item()
        
        test_acc = 100 * test_correct / test_total
        print(f"  Test Accuracy: {test_acc:.2f}%")
        print("-"*30)
    
    total_time = time.time() - start_time
    print(f"Total training time: {total_time:.2f}s")
    print("Quick training completed!")
    
    return model

def test_different_sizes():
    """Test with different subset sizes to find optimal balance"""
    sizes = [500, 1000, 2000, 5000]
    
    print("Testing different subset sizes...")
    print("="*50)
    
    for size in sizes:
        print(f"\nTesting with {size} samples:")
        start_time = time.time()
        
        try:
            model = quick_train(epochs=2, subset_size=size)
            elapsed = time.time() - start_time
            print(f"Time for {size} samples: {elapsed:.2f}s")
        except Exception as e:
            print(f"Error with {size} samples: {e}")
        
        print("-"*30)

if __name__ == "__main__":
    print("Quick Training Script for Wide ResNet")
    print("This uses a subset of CIFAR-10 for faster testing")
    print("="*60)
    
    # Check if we can load data first
    try:
        # Quick data check
        dataset = torchvision.datasets.CIFAR10(root='./data', train=True, download=False)
        print(f"✓ CIFAR-10 data found: {len(dataset)} training samples")
        
        # Run quick training
        model = quick_train(epochs=3, subset_size=1000)
        
        print("\nQuick training successful!")
        print("You can now run the full train1.py with confidence")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        print("Please run test_data.py first to check your data setup")
