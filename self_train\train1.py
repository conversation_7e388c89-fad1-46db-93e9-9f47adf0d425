import torch
import torch.nn as nn
import torch.optim as optim
from networks.wide_resnet_original import wide_resnet_28_10

def train_model():
    # Setup
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = wide_resnet_28_10(num_classes=10, input_channels=3).to(device)
    
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.SGD(model.parameters(), lr=0.1, momentum=0.9, weight_decay=5e-4)
    
    # Load your data (CIFAR-10 example)
    # ... (dataloader setup as shown above)
    
    model.train()
    for epoch in range(3):
        running_loss = 0.0
        for batch_idx, (data, target) in enumerate(trainloader):
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
            
            running_loss += loss.item()
            
        print(f'Epoch {epoch+1}, Loss: {running_loss/len(trainloader):.4f}')

def test_model(model, testloader, device):
    model.eval()
    correct = 0
    total = 0
    
    with torch.no_grad():
        for data, target in testloader:
            data, target = data.to(device), target.to(device)
            outputs = model(data)
            _, predicted = torch.max(outputs.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
    
    accuracy = 100 * correct / total
    print(f'Test Accuracy: {accuracy:.2f}%')
    return accuracy