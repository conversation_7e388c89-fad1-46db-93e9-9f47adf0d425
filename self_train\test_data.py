import os
import torch
import torchvision
import torchvision.transforms as transforms

def check_cifar10_data(data_path='./data'):
    """Check if CIFAR-10 data exists and can be loaded"""
    print("Checking CIFAR-10 data...")
    print(f"Looking in: {os.path.abspath(data_path)}")
    
    # Check directory structure
    cifar_path = os.path.join(data_path, 'cifar-10-batches-py')
    
    if os.path.exists(cifar_path):
        print(f"✓ CIFAR-10 directory found: {cifar_path}")
        
        # List files in the directory
        files = os.listdir(cifar_path)
        print(f"Files found: {files}")
        
        # Check for expected files
        expected_files = [
            'data_batch_1', 'data_batch_2', 'data_batch_3', 
            'data_batch_4', 'data_batch_5', 'test_batch', 'batches.meta'
        ]
        
        missing_files = [f for f in expected_files if f not in files]
        if missing_files:
            print(f"✗ Missing files: {missing_files}")
            return False
        else:
            print("✓ All expected files found")
    else:
        print(f"✗ CIFAR-10 directory not found: {cifar_path}")
        print("Expected structure:")
        print("  ./data/")
        print("    └── cifar-10-batches-py/")
        print("        ├── data_batch_1")
        print("        ├── data_batch_2")
        print("        ├── data_batch_3")
        print("        ├── data_batch_4")
        print("        ├── data_batch_5")
        print("        ├── test_batch")
        print("        └── batches.meta")
        return False
    
    # Try to load the dataset
    try:
        print("\nTesting dataset loading...")
        
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
        ])
        
        # Load training set
        trainset = torchvision.datasets.CIFAR10(root=data_path, train=True, 
                                               download=False, transform=transform)
        print(f"✓ Training set loaded: {len(trainset)} samples")
        
        # Load test set
        testset = torchvision.datasets.CIFAR10(root=data_path, train=False, 
                                              download=False, transform=transform)
        print(f"✓ Test set loaded: {len(testset)} samples")
        
        # Test data loader
        trainloader = torch.utils.data.DataLoader(trainset, batch_size=4, shuffle=True)
        testloader = torch.utils.data.DataLoader(testset, batch_size=4, shuffle=False)
        
        # Get a sample batch
        data_iter = iter(trainloader)
        images, labels = next(data_iter)
        
        print(f"✓ Sample batch shape: {images.shape}")
        print(f"✓ Sample labels: {labels}")
        
        # CIFAR-10 classes
        classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')
        print(f"✓ Sample class names: {[classes[label] for label in labels]}")
        
        print("\n✓ CIFAR-10 data is ready to use!")
        return True
        
    except Exception as e:
        print(f"✗ Error loading dataset: {e}")
        return False

def suggest_solutions():
    """Suggest solutions if data is not found"""
    print("\n" + "="*50)
    print("SOLUTIONS:")
    print("="*50)
    
    print("1. If you have CIFAR-10 data elsewhere:")
    print("   - Move it to ./data/cifar-10-batches-py/")
    print("   - Or update the data_path in your code")
    
    print("\n2. If you need to download CIFAR-10:")
    print("   - Set download=True in the dataset loading")
    print("   - Or run this command:")
    print("     python -c \"import torchvision; torchvision.datasets.CIFAR10('./data', download=True)\"")
    
    print("\n3. For faster loading:")
    print("   - Use SSD storage for data")
    print("   - Increase num_workers in DataLoader")
    print("   - Use pin_memory=True if you have GPU")
    
    print("\n4. Alternative: Use a subset for testing:")
    print("   - Use torch.utils.data.Subset to create smaller datasets")
    print("   - Useful for quick testing and debugging")

if __name__ == "__main__":
    print("CIFAR-10 Data Checker")
    print("="*30)
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"Current directory: {current_dir}")
    
    # Check data
    data_ready = check_cifar10_data('./data')
    
    if not data_ready:
        suggest_solutions()
    else:
        print("\nYour data is ready! You can now run train1.py")
