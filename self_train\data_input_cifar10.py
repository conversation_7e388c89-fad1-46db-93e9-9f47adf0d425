import torch
import torchvision
import torchvision.transforms as transforms
from networks.wide_resnet_original import wide_resnet_28_10

# Data preprocessing
transform_train = transforms.Compose([
    transforms.RandomCrop(32, padding=4),
    transforms.RandomHorizontalFlip(),
    transforms.ToTensor(),
    transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
])

transform_test = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010)),
])

# Load CIFAR-10
trainset = torchvision.datasets.CIFAR10(root='./data', train=True, 
                                       download=True, transform=transform_train)
trainloader = torch.utils.data.DataLoader(trainset, batch_size=128, 
                                         shuffle=True, num_workers=2)

testset = torchvision.datasets.CIFAR10(root='./data', train=False, 
                                      download=True, transform=transform_test)
testloader = torch.utils.data.DataLoader(testset, batch_size=100, 
                                        shuffle=False, num_workers=2)

# Create model for CIFAR-10
model = wide_resnet_28_10(num_classes=10, input_channels=3, dropout_rate=0.3)

# Training loop example
model.train()
for batch_idx, (data, target) in enumerate(trainloader):
    # data shape: [batch_size, 3, 32, 32]
    # target shape: [batch_size]
    
    output = model(data)  # Forward pass
    # output shape: [batch_size, 10]
    break